import { ICalendarEvent } from "../typings/generalCalendar.interface";

interface IPrivateImpegnoParams {
    event: ICalendarEvent | any;
    currentUserId: string | number;
    currentUserGroups?: string[] | number[];
}

interface IPrivateImpegnoDisplayData {
    title: string;
    description: string;
    location: string;
    showFullDetails: boolean;
    isPrivate: boolean;
    canEdit: boolean;
}

interface IFilterPrivateImpegniParams {
    events: ICalendarEvent[] | any[];
    currentUserId: string | number;
    currentUserGroups?: string[] | number[];
}

/**
 * Checks if the current user can view the full details of a private impegno
 * Based on the PHP backend logic and user permissions
 */
export const canViewPrivateImpegno = ({
    event,
    currentUserId,
    currentUserGroups = []
}: IPrivateImpegnoParams): boolean => {
    // If the impegno is not private, everyone can view it
    if (!event.private || event.private === "0" || event.private === 0) {
        return true;
    }

    // Check if current user is the owner/assignee (nomeutente field)
    if (event.nomeutente && event.nomeutente === currentUserId) {
        return true;
    }

    // Check if current user is in the assigned group (fkUtenteGruppo field)
    if (event.fkUtenteGruppo && currentUserGroups.some(groupId => groupId.toString() === event.fkUtenteGruppo.toString())) {
        return true;
    }

    // Check if current user is in the referenti list
    if (event.referenti_ids) {
        const referentiIds = event.referenti_ids.toString().split(',').map((id: string) => id.trim());
        if (referentiIds.includes(currentUserId.toString())) {
            return true;
        }
    }

    // If none of the above conditions are met, user cannot view private details
    return false;
};

/**
 * Checks if the current user can edit a private impegno
 * Same logic as viewing, but could be extended with additional edit restrictions
 */
export const canEditPrivateImpegno = (params: IPrivateImpegnoParams): boolean => {
    return canViewPrivateImpegno(params);
};

/**
 * Gets the appropriate display data for a private impegno based on user permissions
 */
export const getPrivateImpegnoDisplayData = ({
    event,
    currentUserId,
    currentUserGroups = []
}: IPrivateImpegnoParams): IPrivateImpegnoDisplayData => {
    const canView = canViewPrivateImpegno({ event, currentUserId, currentUserGroups });
    const isPrivate = event.private === "1" || event.private === 1;

    if (canView) {
        return {
            title: event.title,
            description: event.annotazioni,
            location: event.citta,
            showFullDetails: true,
            isPrivate: isPrivate,
            canEdit: canEditPrivateImpegno({ event, currentUserId, currentUserGroups })
        };
    } else {
        return {
            title: "Privato",
            description: "",
            location: "",
            showFullDetails: false,
            isPrivate: true,
            canEdit: false
        };
    }
};

/**
 * Filters an array of impegni for display, applying privacy rules
 */
export const filterPrivateImpegniForDisplay = ({
    events,
    currentUserId,
    currentUserGroups = []
}: IFilterPrivateImpegniParams): any[] => {
    return events.map(event => {
        const displayData = getPrivateImpegnoDisplayData({ event, currentUserId, currentUserGroups });

        if (displayData.showFullDetails) {
            return {
                ...event,
                isPrivateAndVisible: displayData.isPrivate,
                canEdit: displayData.canEdit
            };
        } else {
            return {
                ...event,
                title: displayData.title,
                annotazioni: "",
                citta: "",
                descrizionepratica: "",
                isPrivateAndVisible: true,
                canEdit: false,
                // Keep essential fields for calendar functionality
                id: event.id,
                uniqueid: event.uniqueid,
                start: event.start,
                end: event.end,
                type: event.type,
                private: event.private
            };
        }
    });
};
